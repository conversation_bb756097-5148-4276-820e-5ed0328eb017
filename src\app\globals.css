/* ============================================
   CSS RESET & ROOT VARIABLES
   ============================================ */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Brand Colors */
  --primary-cyan: #17b8dd;
  --dark-cyan: #2da8c7;
  --success-green: #51b85f;
  --warning-orange: #f1ad4e;

  /* Dark Theme Colors */
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a23;
  --bg-card: rgba(37, 42, 46, 0.8);
  --bg-transparent: rgba(66, 184, 221, 0.1);
  --bg-glass: rgba(66, 184, 221, 0.05);

  /* Text Colors */
  --text-primary: #e4e4eb;
  --text-secondary: #c4c5c9;
  --text-light: #8b8b9b;
  --text-white: #ffffff;

  /* Effects */
  --glow-cyan: 0 0 20px rgba(23, 184, 221, 0.5);
  --glow-subtle: 0 0 10px rgba(66, 184, 221, 0.3);

  /* Spacing */
  --space-xs: 8px;
  --space-sm: 16px;
  --space-md: 24px;
  --space-lg: 40px;
  --space-xl: 64px;
  --space-2xl: 96px;

  /* Typography */
  --font-sans:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-sans);
  color: var(--text-secondary);
  background: var(--bg-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Typography */
h1 {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 800;
  line-height: 1.1;
  letter-spacing: -0.02em;
  color: #ffffff;
}

h1 .highlight {
  background: linear-gradient(135deg, #17b8dd 0%, #2da8c7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 900;
}

h2 {
  font-size: clamp(2rem, 4vw, 2.75rem);
  font-weight: 700;
  line-height: 1.2;
  color: var(--text-primary);
  letter-spacing: -0.01em;
}

h3 {
  font-size: clamp(1.25rem, 3vw, 1.5rem);
  font-weight: 600;
  line-height: 1.3;
  color: var(--primary-cyan);
}

p {
  font-size: 1.125rem;
  line-height: 1.7;
  color: var(--text-secondary);
}

/* Utilities */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

.section {
  padding: var(--space-xl) 0;
  position: relative;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 14px 32px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, #17b8dd 0%, #2da8c7 100%);
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(23, 184, 221, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(23, 184, 221, 0.5);
}

.btn-secondary {
  background: transparent;
  color: var(--primary-cyan);
  border: 2px solid var(--primary-cyan);
}

.btn-secondary:hover {
  background: var(--bg-transparent);
  box-shadow: var(--glow-subtle);
}

/* ============================================
   HEADER & NAVIGATION
   ============================================ */
header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(26, 26, 35, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  border-bottom: 1px solid rgba(66, 184, 221, 0.1);
}

nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
  padding: 0 var(--space-lg);
}

.logo {
  font-size: 1.75rem;
  font-weight: 800;
  background: linear-gradient(135deg, #17b8dd 0%, #2da8c7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-decoration: none;
}

/* Desktop Navigation */
.desktop-nav {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  list-style: none;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

/* Mobile Navigation - Hidden by default */
.mobile-nav {
  display: none;
  list-style: none;
}

@media (min-width: 769px) {
  .desktop-nav {
    display: flex;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  .mobile-nav {
    display: none !important;
  }
}

/* Desktop Navigation Styles */
.desktop-nav a {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  transition: color 0.3s ease;
  position: relative;
}

.desktop-nav a:hover {
  color: var(--primary-cyan);
}

.desktop-nav a::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-cyan);
  transition: width 0.3s ease;
}

.desktop-nav a:hover::after {
  width: 100%;
}

/* Mobile Menu */
.mobile-menu {
  display: none;
  background: rgba(66, 184, 221, 0.1);
  border: 1px solid rgba(66, 184, 221, 0.2);
  border-radius: 8px;
  cursor: pointer;
  padding: 10px;
  z-index: 1001;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.mobile-menu:hover {
  background: rgba(66, 184, 221, 0.2);
  border-color: rgba(66, 184, 221, 0.4);
  transform: scale(1.05);
}

.mobile-menu span {
  display: block;
  width: 22px;
  height: 2px;
  background: var(--primary-cyan);
  margin: 4px 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 1px;
}

/* Mobile menu animation */
.mobile-menu.active {
  background: rgba(23, 184, 221, 0.2);
  border-color: var(--primary-cyan);
  box-shadow: 0 0 20px rgba(23, 184, 221, 0.3);
}

.mobile-menu.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
  background: var(--text-white);
}

.mobile-menu.active span:nth-child(2) {
  opacity: 0;
  transform: scale(0);
}

.mobile-menu.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
  background: var(--text-white);
}

/* Mobile menu overlay */
.mobile-menu-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  z-index: 998;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mobile-menu-overlay.active {
  display: block;
  opacity: 1;
}

/* Navigation icons */
.nav-icon {
  width: 20px;
  height: 20px;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.mobile-nav a:hover .nav-icon {
  opacity: 1;
}

/* Enhanced Floating Get In Touch Button */
.floating-contact-btn {
  position: fixed;
  bottom: var(--space-lg);
  right: var(--space-lg);
  background: linear-gradient(135deg, #17b8dd 0%, #2da8c7 100%);
  color: #ffffff;
  padding: 14px 28px;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  box-shadow: 0 8px 25px rgba(23, 184, 221, 0.4);
  z-index: 1001;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  align-items: center;
  gap: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  animation: floatPulse 3s ease-in-out infinite;
}

.floating-contact-btn:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 35px rgba(23, 184, 221, 0.6);
  background: linear-gradient(135deg, #2da8c7 0%, #17b8dd 100%);
}

@keyframes floatPulse {
  0%,
  100% {
    box-shadow: 0 8px 25px rgba(23, 184, 221, 0.4);
  }
  50% {
    box-shadow: 0 8px 25px rgba(23, 184, 221, 0.6);
  }
}

/* ============================================
   HERO SECTION
   ============================================ */
#hero {
  padding-top: 160px;
  padding-bottom: var(--space-xl);
  background:
    radial-gradient(
      ellipse at top right,
      rgba(23, 184, 221, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      ellipse at bottom left,
      rgba(35, 166, 199, 0.1) 0%,
      transparent 50%
    );
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-xl);
  align-items: center;
}

.hero-text h1 {
  margin-bottom: var(--space-md);
}

.hero-text .subheadline {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: var(--space-lg);
}

.hero-stats {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm);
  margin-bottom: var(--space-lg);
  font-size: 0.875rem;
  color: var(--text-light);
}

.hero-stats span {
  padding: 6px 12px;
  background: var(--bg-card);
  border-radius: 4px;
  border: 1px solid rgba(66, 184, 221, 0.2);
  white-space: nowrap;
}

.hero-cta {
  display: flex;
  gap: var(--space-md);
  flex-wrap: wrap;
}

.hero-image {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

/* Enhanced Automation Animation */
.automation-animation {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(37, 42, 46, 0.8) 0%, #1a1a23 100%);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(66, 184, 221, 0.1);
}

.automation-animation::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, #17b8dd 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0.15;
  filter: blur(50px);
  animation: breathe 4s ease-in-out infinite;
}

.central-hub {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #17b8dd 0%, #2da8c7 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000;
  font-weight: bold;
  font-size: 1.5rem;
  box-shadow: 0 0 30px rgba(23, 184, 221, 0.6);
  animation: centralPulse 3s ease-in-out infinite;
  z-index: 10;
}

.process-node {
  position: absolute;
  width: 45px;
  height: 45px;
  background: linear-gradient(135deg, #17b8dd 0%, #2da8c7 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000;
  font-weight: bold;
  font-size: 1rem;
  box-shadow: 0 0 20px rgba(23, 184, 221, 0.4);
  animation: nodePulse 2s infinite alternate;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.node-1 {
  top: 15%;
  left: 15%;
  animation-delay: 0s;
}
.node-2 {
  top: 15%;
  right: 15%;
  animation-delay: 0.5s;
}
.node-3 {
  bottom: 15%;
  left: 15%;
  animation-delay: 1s;
}
.node-4 {
  bottom: 15%;
  right: 15%;
  animation-delay: 1.5s;
}
.node-5 {
  top: 50%;
  left: 5%;
  animation-delay: 2s;
}
.node-6 {
  top: 50%;
  right: 5%;
  animation-delay: 2.5s;
}

.automation-line {
  position: absolute;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #17b8dd 50%,
    transparent 100%
  );
  opacity: 0.8;
  animation: dataFlow 3s linear infinite;
  border-radius: 2px;
}

.line-1 {
  top: 18%;
  left: 20%;
  width: 25%;
  height: 3px;
  transform-origin: left center;
  animation-delay: 0s;
}
.line-2 {
  top: 18%;
  right: 20%;
  width: 25%;
  height: 3px;
  transform-origin: right center;
  animation-delay: 0.5s;
}
.line-3 {
  bottom: 18%;
  left: 20%;
  width: 25%;
  height: 3px;
  transform-origin: left center;
  animation-delay: 1s;
}
.line-4 {
  bottom: 18%;
  right: 20%;
  width: 25%;
  height: 3px;
  transform-origin: right center;
  animation-delay: 1.5s;
}
.line-5 {
  top: 50%;
  left: 10%;
  width: 3px;
  height: 25%;
  transform-origin: center top;
  animation: dataFlowVertical 3s linear infinite;
  animation-delay: 2s;
}
.line-6 {
  top: 50%;
  right: 10%;
  width: 3px;
  height: 25%;
  transform-origin: center top;
  animation: dataFlowVertical 3s linear infinite;
  animation-delay: 2.5s;
}

.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #17b8dd;
  border-radius: 50%;
  opacity: 0.6;
  animation: float 6s linear infinite;
}

.particle:nth-child(1) {
  left: 10%;
  animation-delay: 0s;
}
.particle:nth-child(2) {
  left: 30%;
  animation-delay: 1s;
}
.particle:nth-child(3) {
  left: 50%;
  animation-delay: 2s;
}
.particle:nth-child(4) {
  left: 70%;
  animation-delay: 3s;
}
.particle:nth-child(5) {
  left: 90%;
  animation-delay: 4s;
}

/* Animation Keyframes */
@keyframes breathe {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.15;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.25;
  }
}

@keyframes centralPulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    box-shadow: 0 0 30px rgba(23, 184, 221, 0.6);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.05);
    box-shadow: 0 0 40px rgba(23, 184, 221, 0.8);
  }
}

@keyframes nodePulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes dataFlow {
  0% {
    opacity: 0;
    transform: scaleX(0);
  }
  50% {
    opacity: 1;
    transform: scaleX(1);
  }
  100% {
    opacity: 0;
    transform: scaleX(0);
  }
}

@keyframes dataFlowVertical {
  0% {
    opacity: 0;
    transform: scaleY(0);
  }
  50% {
    opacity: 1;
    transform: scaleY(1);
  }
  100% {
    opacity: 0;
    transform: scaleY(0);
  }
}

@keyframes float {
  0% {
    transform: translateY(100vh) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100px) translateX(20px);
    opacity: 0;
  }
}

/* ============================================
   MODAL STYLES
   ============================================ */
.modal {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(8px);
  opacity: 0;
  transition: all 0.4s ease;
  align-items: center;
  justify-content: center;
}

.modal.active {
  display: flex;
  opacity: 1;
}

.modal-content {
  background: linear-gradient(135deg, #1a1a23 0%, rgba(37, 42, 46, 0.95) 100%);
  margin: auto;
  padding: var(--space-xl);
  border-radius: 20px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.7),
    0 0 0 1px rgba(23, 184, 221, 0.1);
  max-width: 750px;
  width: 95%;
  position: relative;
  transform: translateY(30px) scale(0.95);
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid rgba(23, 184, 221, 0.2);
}

.modal.active .modal-content {
  transform: translateY(0) scale(1);
}

.close-button {
  color: var(--text-light);
  font-size: 1.8rem;
  font-weight: bold;
  position: absolute;
  top: 20px;
  right: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
}

.close-button:hover,
.close-button:focus {
  color: #ffffff;
  background: rgba(23, 184, 221, 0.2);
  transform: rotate(90deg);
}

.modal-headline {
  font-size: clamp(1.8rem, 3vw, 2.5rem);
  color: #ffffff;
  margin-bottom: var(--space-sm);
  text-align: center;
  font-weight: 700;
}

.modal-subheadline {
  font-size: 1.1rem;
  color: #c4c5c9;
  margin-bottom: var(--space-xl);
  text-align: center;
}

.contact-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: var(--space-sm);
}

.contact-card {
  background: rgba(66, 184, 221, 0.05);
  padding: var(--space-md);
  border-radius: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid rgba(23, 184, 221, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.contact-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(23, 184, 221, 0.1),
    transparent
  );
  transition: left 0.6s ease;
}

.contact-card:hover::before {
  left: 100%;
}

.contact-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 35px rgba(23, 184, 221, 0.2);
  border-color: #17b8dd;
  background: rgba(23, 184, 221, 0.1);
}

.contact-card .icon {
  font-size: 2rem;
  color: #17b8dd;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.contact-card:hover .icon {
  transform: scale(1.1);
  filter: drop-shadow(0 0 10px rgba(23, 184, 221, 0.5));
}

.contact-card h4 {
  font-size: 1rem;
  color: #ffffff;
  margin-bottom: 4px;
  font-weight: 600;
}

.contact-card p {
  font-size: 0.8rem;
  color: #8b8b9b;
  line-height: 1.3;
}

/* Contact Form Styles */
.contact-form-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #1a1a23 0%, rgba(37, 42, 46, 0.98) 100%);
  border-radius: 20px;
  padding: var(--space-lg);
  animation: slideInFromRight 0.4s ease-out;
}

.contact-form-container {
  height: 100%;
  overflow-y: auto;
  max-height: 80vh;
  padding-right: 8px;
}

/* Custom Scrollbar Styling */
.contact-form-container::-webkit-scrollbar {
  width: 8px;
}

.contact-form-container::-webkit-scrollbar-track {
  background: rgba(66, 184, 221, 0.1);
  border-radius: 4px;
}

.contact-form-container::-webkit-scrollbar-thumb {
  background: rgba(66, 184, 221, 0.4);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.contact-form-container::-webkit-scrollbar-thumb:hover {
  background: rgba(66, 184, 221, 0.6);
}

/* Firefox scrollbar styling */
.contact-form-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(66, 184, 221, 0.4) rgba(66, 184, 221, 0.1);
}

.form-back-btn {
  background: none;
  border: none;
  color: #17b8dd;
  font-size: 0.9rem;
  cursor: pointer;
  margin-bottom: var(--space-md);
  padding: 8px 0;
  transition: color 0.3s ease;
}

.form-back-btn:hover {
  color: #ffffff;
}

.contact-form h3 {
  color: #ffffff;
  margin-bottom: 8px;
  font-size: 1.5rem;
}

.contact-form p {
  color: #8b8b9b;
  margin-bottom: var(--space-lg);
  font-size: 0.95rem;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-md);
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  color: #ffffff;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 8px;
}

.form-group input,
.form-group select,
.form-group textarea {
  background: rgba(66, 184, 221, 0.05);
  border: 1px solid rgba(23, 184, 221, 0.2);
  border-radius: 8px;
  padding: 12px 16px;
  color: #ffffff;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

.form-group select {
  background-color: rgba(66, 184, 221, 0.05);
  color: #ffffff;
}

.form-group select option {
  background-color: #1a1a23;
  color: #ffffff;
  padding: 8px 12px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #17b8dd;
  box-shadow: 0 0 0 3px rgba(23, 184, 221, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-submit {
  margin-top: var(--space-md);
  align-self: flex-start;
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ============================================
   RESPONSIVE DESIGN
   ============================================ */
@media (max-width: 768px) {
  /* Navigation */
  .mobile-menu {
    display: block;
  }

  /* Hide desktop navigation on mobile */
  .desktop-nav {
    display: none !important;
  }

  /* Show mobile navigation */
  .mobile-nav {
    display: none;
    position: fixed;
    top: 25%; /* Moved significantly higher to align first item with close button */
    left: 50%;
    transform: translate(-50%, 0) scale(0.9); /* Changed to not center vertically */
    width: 90%;
    max-width: 400px;
    max-height: 70vh; /* Ensure modal doesn't exceed viewport height */
    background: rgba(26, 26, 35, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(66, 184, 221, 0.2);
    border-radius: 20px;
    padding: var(--space-md) var(--space-md); /* Consistent padding */
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(66, 184, 221, 0.1);
    z-index: 999;
    flex-direction: column;
    gap: var(--space-xs); /* Reduced gap between items */
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-y: auto; /* Allow scrolling if needed */
  }

  .mobile-nav.active {
    display: flex;
    opacity: 1;
    transform: translate(-50%, 0) scale(1); /* Changed to not center vertically */
  }

  .mobile-nav li {
    margin: 0;
    width: 100%;
  }

  .mobile-nav a {
    font-size: 1rem;
    font-weight: 500;
    padding: var(--space-sm) var(--space-md); /* Reduced padding for more compact layout */
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-xs); /* Reduced gap between icon and text */
    border-radius: 12px;
    color: var(--text-white);
    background: rgba(66, 184, 221, 0.05);
    border: 1px solid rgba(66, 184, 221, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .mobile-nav a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(66, 184, 221, 0.1),
      transparent
    );
    transition: left 0.5s ease;
  }

  .mobile-nav a:hover::before {
    left: 100%;
  }

  .mobile-nav a:hover {
    background: rgba(66, 184, 221, 0.15);
    border-color: rgba(66, 184, 221, 0.3);
    color: var(--primary-cyan);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(23, 184, 221, 0.2);
  }

  .mobile-nav a.active {
    background: rgba(23, 184, 221, 0.2);
    border-color: var(--primary-cyan);
    color: var(--primary-cyan);
    box-shadow: 0 0 20px rgba(23, 184, 221, 0.3);
  }

  .nav-cta {
    display: none;
  }

  /* Hero Section */
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
    text-align: center;
  }

  .hero-stats {
    flex-direction: column;
    gap: var(--space-sm);
  }

  .hero-cta {
    justify-content: center;
    flex-direction: column;
    gap: var(--space-sm);
  }

  .hero-cta .btn {
    width: 100%;
    max-width: 320px;
    padding: 16px 24px;
    font-size: 1rem;
    font-weight: 600;
  }

  /* Forms */
  .form-row {
    grid-template-columns: 1fr;
  }

  .contact-options {
    grid-template-columns: 1fr;
  }

  /* Modal adjustments */
  .modal-content {
    width: 95%;
    max-width: none;
    margin: 10px;
    max-height: 90vh;
  }

  .contact-form-container {
    max-height: 75vh;
  }

  /* Floating button */
  .floating-contact-btn {
    bottom: var(--space-md);
    right: var(--space-md);
    padding: 12px 20px;
    font-size: 0.9rem;
  }

  /* Calendly Modal Mobile */
  .calendly-modal-overlay {
    padding: var(--space-sm);
  }

  .calendly-modal-content {
    height: 95vh;
    max-height: none;
    border-radius: 12px;
  }

  .calendly-modal-header {
    padding: var(--space-sm);
  }

  .calendly-modal-header h2 {
    font-size: 1.2rem;
  }

  .calendly-success,
  .calendly-error {
    padding: var(--space-md);
    max-width: 350px;
  }

  /* Animation */
  .automation-animation {
    height: 300px;
  }

  .central-hub {
    width: 60px;
    height: 60px;
    font-size: 1.2rem;
  }

  /* Typography adjustments */
  h1 {
    font-size: clamp(2rem, 8vw, 3rem);
  }

  .container {
    padding: 0 var(--space-sm);
  }
}

/* Tablet styles */
@media (max-width: 1024px) and (min-width: 769px) {
  .hero-content {
    gap: var(--space-lg);
  }

  .desktop-nav {
    gap: var(--space-md);
    font-size: 0.9rem;
  }

  .nav-cta .btn {
    padding: 10px 20px;
    font-size: 0.9rem;
  }

  .hero-stats {
    flex-wrap: wrap;
    justify-content: center;
  }

  .container {
    padding: 0 var(--space-md);
  }
}

/* Large mobile styles */
@media (max-width: 640px) and (min-width: 481px) {
  .mobile-nav a {
    font-size: 0.95rem;
    padding: var(--space-xs) var(--space-sm);
  }

  .hero-cta {
    flex-direction: column;
    align-items: center;
    gap: var(--space-sm);
  }

  .hero-cta .btn {
    width: 100%;
    max-width: 300px;
    padding: 15px 24px;
    font-size: 1rem;
  }
}

/* Small mobile styles */
@media (max-width: 480px) {
  .modal-content {
    width: 98%;
    margin: 5px;
    padding: var(--space-md);
  }

  .hero-cta .btn {
    padding: 14px 20px;
    font-size: 0.95rem;
    width: 100%;
    max-width: 280px;
    font-weight: 600;
  }

  .floating-contact-btn {
    padding: 10px 16px;
    font-size: 0.8rem;
    bottom: var(--space-sm);
    right: var(--space-sm);
  }

  nav {
    padding: 0 var(--space-sm);
  }

  .logo {
    font-size: 1.4rem;
  }

  .mobile-nav a {
    font-size: 0.9rem;
    padding: var(--space-xs) var(--space-sm);
  }

  /* Ensure mobile nav fits on very small screens */
  .mobile-nav {
    max-height: 65vh;
    top: 20%; /* Even higher positioning for small screens to ensure all items are visible */
  }

  /* Calendly Modal Small Mobile */
  .calendly-modal-overlay {
    padding: 8px;
  }

  .calendly-modal-content {
    height: 98vh;
    border-radius: 8px;
  }

  .calendly-modal-header {
    padding: 12px;
  }

  .calendly-modal-header h2 {
    font-size: 1.1rem;
  }

  .calendly-success,
  .calendly-error {
    padding: var(--space-sm);
    max-width: 300px;
  }

  .process-node {
    width: 35px;
    height: 35px;
    font-size: 0.8rem;
  }

  h1 {
    font-size: clamp(1.8rem, 7vw, 2.5rem);
  }

  .section {
    padding: var(--space-lg) 0;
  }
}

/* ============================================
   SECTION STYLES
   ============================================ */
.section-header {
  text-align: center;
  margin-bottom: var(--space-xl);
}

.section-header h2 {
  margin-bottom: var(--space-sm);
}

.section-header p {
  font-size: 1.125rem;
  color: var(--text-light);
}

/* Insights Section */
.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
}

.insight-card {
  background: var(--bg-card);
  padding: var(--space-lg);
  border-radius: 12px;
  border: 1px solid rgba(66, 184, 221, 0.1);
  transition: all 0.3s ease;
}

.insight-card:hover {
  transform: translateY(-5px);
  border-color: var(--primary-cyan);
  box-shadow: var(--glow-subtle);
}

.insight-category {
  color: var(--primary-cyan);
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: var(--space-sm);
}

.insight-card h3 {
  margin-bottom: var(--space-sm);
}

.insight-card p {
  margin-bottom: var(--space-md);
}

.insight-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.read-time {
  color: var(--text-light);
  font-size: 0.875rem;
}

.read-more {
  background: none;
  border: none;
  color: var(--primary-cyan);
  font-weight: 600;
  cursor: pointer;
  transition: color 0.3s ease;
}

.read-more:hover {
  color: var(--dark-cyan);
}

/* Story Section */
.story-content-wrapper {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--space-xl);
  align-items: center;
}

.story-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  background: var(--bg-card);
  border-radius: 12px;
  border: 1px solid rgba(66, 184, 221, 0.1);
}

.story-label {
  color: var(--primary-cyan);
  font-size: 1.25rem;
  font-weight: 600;
}

.story-content h2 {
  margin-bottom: var(--space-sm);
}

.story-content h3 {
  margin-bottom: var(--space-md);
  color: var(--text-secondary);
}

.story-content p {
  margin-bottom: var(--space-md);
}

/* Case Study Section */
.case-study-content {
  max-width: 1000px;
  margin: 0 auto;
}

.case-study-header {
  text-align: center;
  margin-bottom: var(--space-xl);
}

.case-study-label {
  color: var(--primary-cyan);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.case-study-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-xl);
  align-items: start;
}

.case-study-details > div {
  margin-bottom: var(--space-lg);
}

.case-study-details h3 {
  margin-bottom: var(--space-sm);
}

.metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-md);
  margin-top: var(--space-md);
}

.metric {
  text-align: center;
  padding: var(--space-md);
  background: var(--bg-card);
  border-radius: 8px;
  border: 1px solid rgba(66, 184, 221, 0.1);
}

.metric-value {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  color: var(--primary-cyan);
  margin-bottom: 4px;
}

.metric-label {
  font-size: 0.875rem;
  color: var(--text-light);
}

.case-study-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.case-study-image-placeholder {
  width: 100%;
  height: 300px;
  background: var(--bg-card);
  border-radius: 12px;
  border: 1px solid rgba(66, 184, 221, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
}

/* ============================================
   CALENDLY MODAL STYLES - BRANDED
   ============================================ */
.calendly-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(12px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-md);
  animation: fadeIn 0.3s ease-out;
}

.calendly-modal-content {
  background: var(--bg-secondary);
  border-radius: 16px;
  width: 100%;
  max-width: 900px;
  height: 90vh;
  max-height: 700px;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(23, 184, 221, 0.2);
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.6),
    0 0 0 1px rgba(23, 184, 221, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  animation: slideUp 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
}

.calendly-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md);
  border-bottom: 1px solid rgba(23, 184, 221, 0.15);
  background: linear-gradient(
    135deg,
    rgba(23, 184, 221, 0.08) 0%,
    rgba(45, 168, 199, 0.05) 100%
  );
  backdrop-filter: blur(10px);
}

.calendly-modal-header h2 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.375rem;
  font-weight: 600;
  background: linear-gradient(135deg, #ffffff 0%, var(--primary-cyan) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.calendly-close-btn {
  background: rgba(23, 184, 221, 0.1);
  border: 1px solid rgba(23, 184, 221, 0.2);
  color: var(--text-secondary);
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.calendly-close-btn:hover {
  background: rgba(23, 184, 221, 0.2);
  border-color: var(--primary-cyan);
  color: var(--primary-cyan);
  box-shadow: 0 0 15px rgba(23, 184, 221, 0.3);
  transform: scale(1.05);
}

.calendly-modal-body {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: var(--bg-primary);
}

.calendly-iframe-container {
  height: 100%;
  width: 100%;
}

.calendly-iframe-container iframe {
  border-radius: 0 0 16px 16px;
  background: var(--bg-primary);
}

/* Loading State */
.calendly-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: var(--text-secondary);
  z-index: 10;
}

.loading-spinner {
  margin: 0 auto var(--space-md);
}

.loading-spinner .spinner {
  width: 48px;
  height: 48px;
  border: 3px solid rgba(23, 184, 221, 0.2);
  border-top: 3px solid var(--primary-cyan);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  box-shadow: 0 0 20px rgba(23, 184, 221, 0.3);
}

.calendly-loading p {
  font-size: 1rem;
  color: var(--text-light);
  margin: 0;
}

/* Success State */
.calendly-success {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: var(--text-primary);
  z-index: 10;
  background: var(--bg-secondary);
  padding: var(--space-xl);
  border-radius: 16px;
  border: 1px solid rgba(23, 184, 221, 0.2);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  max-width: 400px;
  width: 90%;
}

.success-icon {
  margin: 0 auto var(--space-md);
  color: var(--success-green);
  animation: successPulse 2s ease-in-out infinite;
}

.calendly-success h3 {
  color: var(--primary-cyan);
  margin-bottom: var(--space-sm);
  font-size: 1.5rem;
}

.calendly-success p {
  margin-bottom: var(--space-sm);
  color: var(--text-secondary);
}

.booking-details {
  background: rgba(23, 184, 221, 0.05);
  border: 1px solid rgba(23, 184, 221, 0.1);
  border-radius: 8px;
  padding: var(--space-sm);
  margin: var(--space-md) 0;
  text-align: left;
}

.booking-details p {
  margin-bottom: var(--space-xs);
  font-size: 0.9rem;
}

.success-note {
  font-size: 0.875rem;
  color: var(--text-light);
  font-style: italic;
}

/* Error State */
.calendly-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: var(--text-primary);
  z-index: 10;
  background: var(--bg-secondary);
  padding: var(--space-xl);
  border-radius: 16px;
  border: 1px solid rgba(241, 173, 78, 0.2);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  max-width: 400px;
  width: 90%;
}

.error-icon {
  margin: 0 auto var(--space-md);
  color: var(--warning-orange);
}

.calendly-error h3 {
  color: var(--warning-orange);
  margin-bottom: var(--space-sm);
}

.calendly-fallback-btn {
  display: inline-block;
  background: linear-gradient(
    135deg,
    var(--primary-cyan) 0%,
    var(--dark-cyan) 100%
  );
  color: #ffffff;
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  margin-top: var(--space-md);
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(23, 184, 221, 0.3);
}

.calendly-fallback-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(23, 184, 221, 0.4);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes successPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* ============================================
   TESTIMONIALS SECTION (MOSAIC)
   ============================================ */
#testimonials {
  background: #1a1a1a;
  overflow: hidden;
  position: relative;
}

.testimonials-header {
  text-align: center;
  margin-bottom: var(--space-xl);
}

.testimonials-header h2 {
  color: var(--text-primary);
}

.testimonials-header p {
  color: rgba(255, 255, 255, 0.7);
  margin-top: var(--space-sm);
}

.testimonial-mosaic {
  position: relative;
  height: 700px;
  overflow: hidden;
  max-width: 1400px;
  margin: 0 auto;
}

.mosaic-container {
  display: flex;
  gap: 20px;
  animation: scrollMosaic 50s linear infinite;
  width: max-content;
  padding: 20px 0;
}

.mosaic-container:hover {
  animation-play-state: paused;
}

.mosaic-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 360px;
}

.mosaic-column:nth-child(even) {
  margin-top: 60px;
}

.mosaic-column:nth-child(3n) {
  margin-top: 30px;
}

@keyframes scrollMosaic {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.testimonial-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 24px;
  border-radius: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.08);
}

.testimonial-card.small {
  padding: 20px;
  min-height: 120px;
}

.testimonial-card.medium {
  min-height: 200px;
}

.testimonial-card.large {
  padding: 32px;
  min-height: 280px;
}

.testimonial-card.video {
  background: #000;
  position: relative;
  min-height: 240px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-play {
  width: 60px;
  height: 60px;
  background: rgba(195, 136, 32, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.video-play:hover {
  transform: scale(1.1);
  background: var(--warning-orange);
}

.video-play::after {
  content: '';
  width: 0;
  height: 0;
  border-left: 18px solid var(--text-primary);
  border-top: 12px solid transparent;
  border-bottom: 12px solid transparent;
  margin-left: 4px;
}

.testimonial-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.testimonial-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--primary-cyan);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000;
  font-weight: 600;
  font-size: 18px;
}

.testimonial-info {
  flex: 1;
}

.testimonial-author {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.testimonial-role {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.testimonial-text {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

.testimonial-quote {
  font-size: 1.25rem;
  font-weight: 300;
  font-style: italic;
}

.testimonial-stat {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--warning-orange);
  margin-bottom: 8px;
}

.testimonial-metric {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
}

.testimonial-accent {
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
}

.testimonial-card.gold-accent .testimonial-accent {
  background: var(--warning-orange);
}

.testimonial-card.green-accent .testimonial-accent {
  background: var(--success-green);
}

.fade-edge-left,
.fade-edge-right {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 120px;
  pointer-events: none;
  z-index: 10;
}

.fade-edge-left {
  left: 0;
  background: linear-gradient(to right, #1a1a1a, transparent);
}

.fade-edge-right {
  right: 0;
  background: linear-gradient(to left, #1a1a1a, transparent);
}

/* FAQ Section */
.faq-container {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  border: 1px solid rgba(66, 184, 221, 0.1);
  border-radius: 8px;
  margin-bottom: var(--space-sm);
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq-item:hover {
  border-color: var(--primary-cyan);
}

.faq-question {
  width: 100%;
  padding: var(--space-md);
  background: var(--bg-card);
  border: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.faq-question:hover {
  background: rgba(66, 184, 221, 0.05);
}

.faq-icon {
  font-size: 1.5rem;
  color: var(--primary-cyan);
  transition: transform 0.3s ease;
}

.faq-item.active .faq-icon {
  transform: rotate(180deg);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.faq-item.active .faq-answer {
  max-height: 200px;
}

.faq-answer p {
  padding: var(--space-md);
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Footer Section */
footer {
  background: var(--bg-secondary);
  border-top: 1px solid rgba(66, 184, 221, 0.1);
}

.footer-content {
  padding: var(--space-xl) 0 var(--space-lg);
}

.footer-main {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--space-xl);
  margin-bottom: var(--space-lg);
}

.footer-brand h3 {
  color: var(--primary-cyan);
  margin-bottom: var(--space-sm);
}

.footer-brand p {
  color: var(--text-light);
  margin-bottom: var(--space-sm);
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-lg);
}

.footer-column h4 {
  color: var(--text-primary);
  margin-bottom: var(--space-md);
  font-size: 1.125rem;
}

.footer-column ul {
  list-style: none;
}

.footer-column li {
  margin-bottom: var(--space-sm);
}

.footer-column a {
  color: var(--text-light);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-column a:hover {
  color: var(--primary-cyan);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--space-lg);
  border-top: 1px solid rgba(66, 184, 221, 0.1);
}

.footer-social {
  display: flex;
  gap: var(--space-md);
}

.footer-social a {
  color: var(--text-light);
  transition: color 0.3s ease;
}

.footer-social a:hover {
  color: var(--primary-cyan);
}

.footer-copyright p {
  color: var(--text-light);
  font-size: 0.875rem;
}

/* Additional responsive styles */
@media (max-width: 768px) {
  .story-content-wrapper {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .case-study-grid {
    grid-template-columns: 1fr;
  }

  .metrics {
    grid-template-columns: 1fr;
  }

  .footer-main {
    grid-template-columns: 1fr;
  }

  .footer-links {
    grid-template-columns: 1fr;
  }

  .footer-bottom {
    flex-direction: column;
    gap: var(--space-md);
    text-align: center;
  }
}

/* ============================================
   POST-BOOKING FORM STYLES
   ============================================ */

.post-booking-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

.post-booking-modal {
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

.form-container {
  padding: 32px;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-header h3 {
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.form-header p {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
}

.booking-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  color: #374151;
  font-weight: 500;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #06b6d4;
  box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.skip-btn {
  padding: 12px 24px;
  border: 1px solid #d1d5db;
  background: white;
  color: #64748b;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.skip-btn:hover {
  background: #f8fafc;
  border-color: #94a3b8;
}

.submit-btn {
  padding: 12px 24px;
  border: none;
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: white;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #0891b2, #0e7490);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.success-state {
  padding: 48px 32px;
  text-align: center;
}

.success-icon {
  font-size: 4rem;
  margin-bottom: 16px;
}

.success-state h3 {
  color: #10b981;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.success-state p {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
}

/* Mobile Responsiveness for Post-Booking Form */
@media (max-width: 768px) {
  .post-booking-overlay {
    padding: 16px;
  }

  .post-booking-modal {
    max-height: 95vh;
  }

  .form-container {
    padding: 24px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .form-actions {
    flex-direction: column;
  }

  .skip-btn,
  .submit-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .form-container {
    padding: 20px;
  }

  .form-header h3 {
    font-size: 1.25rem;
  }

  .success-state {
    padding: 32px 20px;
  }
}
