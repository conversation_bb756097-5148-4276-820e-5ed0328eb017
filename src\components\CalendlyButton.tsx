'use client'

import { useState } from 'react'
import CalendlyBookingModal from './CalendlyBookingModal'
import { trackEvent } from '@/lib/analytics'

interface CalendlyButtonProps {
  children: React.ReactNode
  className?: string
  ctaLocation: string // Required: track which CTA this is
  calendlyUrl?: string
  variant?: 'button' | 'link' | 'card'
  onClick?: () => void // Optional additional click handler
}

export default function CalendlyButton({
  children,
  className = '',
  ctaLocation,
  calendlyUrl,
  variant = 'button',
  onClick
}: CalendlyButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false)

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()
    
    // Track the CTA click
    trackEvent('calendly_cta_clicked', { 
      cta_location: ctaLocation,
      variant 
    })
    
    // Call additional click handler if provided
    if (onClick) {
      onClick()
    }
    
    // Open the modal
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
  }

  // Render based on variant
  if (variant === 'button') {
    return (
      <>
        <button
          className={className}
          onClick={handleClick}
          type="button"
        >
          {children}
        </button>
        <CalendlyBookingModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          calendlyUrl={calendlyUrl}
          ctaLocation={ctaLocation}
        />
      </>
    )
  }

  if (variant === 'link') {
    return (
      <>
        <a
          href="#"
          className={className}
          onClick={handleClick}
        >
          {children}
        </a>
        <CalendlyBookingModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          calendlyUrl={calendlyUrl}
          ctaLocation={ctaLocation}
        />
      </>
    )
  }

  if (variant === 'card') {
    return (
      <>
        <div
          className={className}
          onClick={handleClick}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              handleClick(e as any)
            }
          }}
        >
          {children}
        </div>
        <CalendlyBookingModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          calendlyUrl={calendlyUrl}
          ctaLocation={ctaLocation}
        />
      </>
    )
  }

  return null
}
