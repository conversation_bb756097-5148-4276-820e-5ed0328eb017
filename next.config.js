/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['localhost'],
    formats: ['image/webp', 'image/avif'],
  },
  // Enable strict mode for better development experience
  reactStrictMode: true,

  // Optimize for production
  swcMinify: true,

  // Configure headers for security and performance
  async headers() {
    const securityHeaders = [
      {
        key: 'X-Frame-Options',
        value: 'DENY',
      },
      {
        key: 'X-Content-Type-Options',
        value: 'nosniff',
      },
      {
        key: 'Referrer-Policy',
        value: 'origin-when-cross-origin',
      },
    ]

    // Only add CSP in production to avoid development issues
    if (process.env.NODE_ENV === 'production') {
      securityHeaders.push({
        key: 'Content-Security-Policy',
        value: [
          "default-src 'self'",
          "script-src 'self' 'unsafe-inline' https://assets.calendly.com https://calendly.com https://vercel.live https://*.vercel.app https://*.vercel.com",
          "style-src 'self' 'unsafe-inline' https://assets.calendly.com",
          "img-src 'self' data: https:",
          "connect-src 'self' https://*.supabase.co wss://*.supabase.co https://vercel.live wss://vercel.live",
          "frame-src 'self' https://calendly.com https://*.calendly.com",
        ].join('; '),
      })
    }

    return [
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
    ]
  },

  // Configure redirects if needed
  async redirects() {
    return [
      // Add redirects here if needed
    ]
  },
}

module.exports = nextConfig
