## Debug API Route sample
// app/api/debug-env/route.ts
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  // Check all environment variables
  const envCheck = {
    supabaseUrl: {
      value: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set',
      length: process.env.NEXT_PUBLIC_SUPABASE_URL?.length || 0,
      preview: process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 20) + '...' || 'undefined'
    },
    anonKey: {
      value: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set',
      length: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length || 0,
      preview: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 20) + '...' || 'undefined'
    },
    serviceKey: {
      value: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Not set',
      length: process.env.SUPABASE_SERVICE_ROLE_KEY?.length || 0,
      preview: process.env.SUPABASE_SERVICE_ROLE_KEY?.substring(0, 20) + '...' || 'undefined'
    },
    calendlyUrl: {
      value: process.env.NEXT_PUBLIC_CALENDLY_URL ? 'Set' : 'Not set',
      length: process.env.NEXT_PUBLIC_CALENDLY_URL?.length || 0,
      preview: process.env.NEXT_PUBLIC_CALENDLY_URL || 'undefined'
    },
    nodeEnv: process.env.NODE_ENV,
    vercelEnv: process.env.VERCEL_ENV,
    allEnvKeys: Object.keys(process.env)
      .filter(key => key.includes('SUPABASE') || key.includes('CALENDLY'))
      .sort()
  }

  // Test Supabase connection
  let supabaseTest = 'Not tested'
  try {
    if (process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      // Simple fetch test to Supabase
      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/`, {
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`
        }
      })
      supabaseTest = response.ok ? 'Connection successful' : `Connection failed: ${response.status}`
    }
  } catch (error) {
    supabaseTest = `Connection error: ${error instanceof Error ? error.message : 'Unknown error'}`
  }

  return NextResponse.json({
    message: 'Environment Variables Debug',
    timestamp: new Date().toISOString(),
    environment: envCheck,
    supabaseConnectionTest: supabaseTest
  })
}

## Supabase Client Configuration
// lib/supabase.ts
import { createClient } from '@supabase/supabase-js'

// Environment variables with validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// Validate environment variables
if (!supabaseUrl) {
  console.error('❌ NEXT_PUBLIC_SUPABASE_URL is not set')
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable')
}

if (!supabaseAnonKey) {
  console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY is not set')
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable')
}

// Client for public operations (using anon key)
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  }
})

// Admin client for server-side operations (using service role key)
export const supabaseAdmin = supabaseServiceKey 
  ? createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
        detectSessionInUrl: false
      }
    })
  : null

// Utility function to test connection
export async function testSupabaseConnection() {
  try {
    const { data, error } = await supabase
      .from('bookings')
      .select('count')
      .limit(1)
    
    if (error) {
      console.error('❌ Supabase connection test failed:', error)
      return { success: false, error: error.message }
    }
    
    console.log('✅ Supabase connection test successful')
    return { success: true, data }
  } catch (error) {
    console.error('❌ Supabase connection test error:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

// Enhanced logging for debugging
export function logSupabaseConfig() {
  console.log('📊 Supabase Configuration:')
  console.log('  URL:', supabaseUrl ? '✅ Set' : '❌ Missing')
  console.log('  Anon Key:', supabaseAnonKey ? '✅ Set' : '❌ Missing')
  console.log('  Service Key:', supabaseServiceKey ? '✅ Set' : '❌ Missing')
  console.log('  Environment:', process.env.NODE_ENV)
  console.log('  Vercel Env:', process.env.VERCEL_ENV)
}

## Calendly Modal
'use client'

import { useEffect, useState, useCallback } from 'react'
import { createClient } from '@supabase/supabase-js'

interface CalendlyBookingModalProps {
  isOpen: boolean
  onClose: () => void
  calendlyUrl?: string
}

// Supabase client setup with validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Check if Supabase is configured
const isSupabaseConfigured = supabaseUrl && supabaseAnonKey

const supabase = isSupabaseConfigured 
  ? createClient(supabaseUrl, supabaseAnonKey)
  : null

export default function CalendlyBookingModal({
  isOpen,
  onClose,
  calendlyUrl = process.env.NEXT_PUBLIC_CALENDLY_URL || 'https://calendly.com/your-scheduling-link'
}: CalendlyBookingModalProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [bookingSuccess, setBookingSuccess] = useState(false)
  const [bookingData, setBookingData] = useState<any>(null)

  // Save booking data to Supabase (Free Plan - Message Events Only)
  const saveBookingToSupabase = async (eventData: any) => {
    // Check if Supabase is configured
    if (!isSupabaseConfigured) {
      console.warn('⚠️ Event tracking skipped: Supabase not configured')
      console.log('📋 Booking data would be saved:', eventData)
      return false
    }

    if (!supabase) {
      console.error('❌ Supabase client not initialized')
      return false
    }

    try {
      // Extract data from Calendly message event
      const payload = eventData.payload || eventData
      
      // Create booking record with available data
      const bookingRecord = {
        calendly_id: payload.event?.uuid || `booking_${Date.now()}`,
        event_type: payload.event?.event_type?.name || 'Consultation',
        event_name: payload.event?.name || 'Booking Call',
        invitee_email: payload.invitee?.email || '<EMAIL>',
        invitee_name: payload.invitee?.name || 'Unknown Client',
        invitee_timezone: payload.invitee?.timezone || 'UTC',
        scheduled_at: payload.event?.start_time || new Date().toISOString(),
        start_time: payload.event?.start_time || new Date().toISOString(),
        end_time: payload.event?.end_time || new Date().toISOString(),
        status: 'active',
        meeting_url: payload.event?.location?.join_url || '',
        location: payload.event?.location?.type || 'Online',
        source: 'website_popup',
        utm_source: 'portfolio',
        utm_medium: 'popup',
        utm_campaign: 'booking',
        raw_data: eventData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      console.log('💾 Attempting to save booking to Supabase:', bookingRecord)

      const { data, error } = await supabase
        .from('bookings')
        .insert([bookingRecord])
        .select()

      if (error) {
        console.error('❌ Error saving booking:', error)
        console.error('📋 Error details:', {
          message: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint
        })
        return false
      }

      console.log('✅ Booking saved successfully:', data)
      setBookingData(data[0])
      return true
    } catch (error) {
      console.error('❌ Error in saveBookingToSupabase:', error)
      return false
    }
  }

  // Handle Calendly message events (Free Plan Method)
  useEffect(() => {
    if (!isOpen) return

    const handleCalendlyMessage = async (e: MessageEvent) => {
      // Security: Only accept messages from Calendly
      if (e.origin !== 'https://calendly.com') return

      const { event, payload } = e.data

      console.log('📅 Calendly Event:', event, payload)

      switch (event) {
        case 'calendly.profile_page_viewed':
          console.log('👀 Profile page viewed')
          setIsLoading(false)
          setHasError(false)
          break

        case 'calendly.event_type_viewed':
          console.log('📋 Event type viewed')
          setIsLoading(false)
          setHasError(false)
          break

        case 'calendly.date_and_time_selected':
          console.log('🕐 Date and time selected')
          break

        case 'calendly.event_scheduled':
          console.log('🎉 Event scheduled!', payload)
          setBookingSuccess(true)
          
          // Save to Supabase
          const saved = await saveBookingToSupabase(e.data)
          if (saved) {
            console.log('✅ Booking data saved to database')
          }
          
          // Auto-close modal after 3 seconds
          setTimeout(() => {
            handleClose()
          }, 3000)
          break

        case 'calendly.page_height':
          // Handle iframe height changes
          console.log('📏 Page height changed:', payload)
          break

        default:
          console.log('ℹ️ Other Calendly event:', event, payload)
      }
    }

    window.addEventListener('message', handleCalendlyMessage)
    return () => window.removeEventListener('message', handleCalendlyMessage)
  }, [isOpen])

  // Handle iframe load events
  const handleIframeLoad = useCallback(() => {
    console.log('🔄 Calendly iframe loaded')
    // Give iframe time to initialize
    setTimeout(() => {
      if (isLoading) {
        setIsLoading(false)
      }
    }, 2000)
  }, [isLoading])

  // Handle iframe error
  const handleIframeError = useCallback(() => {
    console.error('❌ Calendly iframe failed to load')
    setHasError(true)
    setIsLoading(false)
  }, [])

  // Handle modal close
  const handleClose = useCallback(() => {
    setBookingSuccess(false)
    setBookingData(null)
    setIsLoading(true)
    setHasError(false)
    onClose()
  }, [onClose])

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'auto'
    }
  }, [isOpen, handleClose])

  // Don't render if not open
  if (!isOpen) return null

  return (
    <div className="calendly-modal-overlay" onClick={handleClose}>
      <div
        className="calendly-modal-content"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Modal Header */}
        <div className="calendly-modal-header">
          <h2>Schedule Your Call</h2>
          <button
            className="calendly-close-btn"
            onClick={handleClose}
            aria-label="Close booking modal"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path
                d="M18 6L6 18M6 6L18 18"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        {/* Modal Body */}
        <div className="calendly-modal-body">
          {/* Success State */}
          {bookingSuccess && (
            <div className="calendly-success">
              <div className="success-icon">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                  <path d="m9 12 2 2 4-4" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
              <h3>Booking Confirmed! 🎉</h3>
              <p>Your call has been scheduled successfully.</p>
              <p>You'll receive a confirmation email from Calendly shortly.</p>
              {bookingData && (
                <div className="booking-details">
                  <p><strong>Event:</strong> {bookingData.event_name}</p>
                  <p><strong>Date:</strong> {new Date(bookingData.start_time).toLocaleDateString()}</p>
                  <p><strong>Time:</strong> {new Date(bookingData.start_time).toLocaleTimeString()}</p>
                </div>
              )}
              <p className="success-note">This window will close automatically...</p>
            </div>
          )}

          {/* Loading State */}
          {isLoading && !bookingSuccess && (
            <div className="calendly-loading">
              <div className="loading-spinner">
                <div className="spinner"></div>
              </div>
              <p>Loading your booking calendar...</p>
            </div>
          )}

          {/* Error State */}
          {hasError && !bookingSuccess && (
            <div className="calendly-error">
              <div className="error-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                  <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" strokeWidth="2"/>
                  <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
              <h3>Unable to load calendar</h3>
              <p>We're having trouble loading the booking calendar. Please try the direct link below.</p>
              <a
                href={calendlyUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="calendly-fallback-btn"
              >
                Open Calendly in New Tab
              </a>
            </div>
          )}

          {/* Calendly Iframe */}
          {!isLoading && !hasError && !bookingSuccess && (
            <div className="calendly-iframe-container">
              <iframe
                src={`${calendlyUrl}?embed_domain=${typeof window !== 'undefined' ? window.location.hostname : 'localhost'}&embed_type=Inline&hide_gdpr_banner=1`}
                width="100%"
                height="600"
                frameBorder="0"
                title="Schedule a meeting"
                onLoad={handleIframeLoad}
                onError={handleIframeError}
                style={{ 
                  border: 'none',
                  borderRadius: '0 0 20px 20px'
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

## Bookings API Route
// app/api/bookings/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// Initialize Supabase client
const supabase = supabaseUrl && supabaseAnonKey 
  ? createClient(supabaseUrl, supabaseAnonKey)
  : null

const supabaseAdmin = supabaseUrl && supabaseServiceKey
  ? createClient(supabaseUrl, supabaseServiceKey)
  : null

export async function GET(request: NextRequest) {
  try {
    // Debug environment variables
    const envStatus = {
      supabaseUrl: supabaseUrl ? 'Set' : 'Not set',
      anonKey: supabaseAnonKey ? 'Set' : 'Not set',
      serviceKey: supabaseServiceKey ? 'Set' : 'Not set',
      calendlyUrl: process.env.NEXT_PUBLIC_CALENDLY_URL ? 'Set' : 'Not set',
      nodeEnv: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV
    }

    // If no Supabase connection, return debug info
    if (!supabase) {
      return NextResponse.json({
        message: 'Bookings API endpoint - Supabase not configured',
        environment: envStatus,
        error: 'Missing Supabase environment variables',
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

    // Test Supabase connection and get bookings
    const { data: bookings, error } = await supabase
      .from('bookings')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10)

    if (error) {
      console.error('❌ Supabase query error:', error)
      return NextResponse.json({
        message: 'Bookings API endpoint - Database error',
        environment: envStatus,
        error: error.message,
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

    // Return success response
    return NextResponse.json({
      message: 'Bookings API endpoint - Success',
      environment: envStatus,
      bookings: bookings || [],
      count: bookings?.length || 0,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ API Route Error:', error)
    return NextResponse.json({
      message: 'Bookings API endpoint - Server error',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    if (!supabase) {
      return NextResponse.json({
        error: 'Supabase not configured',
        message: 'Missing environment variables'
      }, { status: 500 })
    }

    const bookingData = await request.json()
    
    const { data, error } = await supabase
      .from('bookings')
      .insert([bookingData])
      .select()

    if (error) {
      console.error('❌ Error creating booking:', error)
      return NextResponse.json({
        error: error.message,
        details: error.details
      }, { status: 500 })
    }

    return NextResponse.json({
      message: 'Booking created successfully',
      booking: data[0]
    })

  } catch (error) {
    console.error('❌ POST Error:', error)
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}