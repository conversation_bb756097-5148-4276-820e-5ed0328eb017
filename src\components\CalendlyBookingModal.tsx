'use client'

import { useEffect, useState, useCallback } from 'react'
import { db } from '@/lib/supabase'
import { trackEvent } from '@/lib/analytics'

interface CalendlyBookingModalProps {
  isOpen: boolean
  onClose: () => void
  calendlyUrl?: string
  ctaLocation?: string
}

export default function CalendlyBookingModal({
  isOpen,
  onClose,
  calendlyUrl = process.env.NEXT_PUBLIC_CALENDLY_URL ||
    'https://calendly.com/your-scheduling-link',
  ctaLocation = 'unknown',
}: CalendlyBookingModalProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [bookingSuccess, setBookingSuccess] = useState(false)
  const [bookingData, setBookingData] = useState<any>(null)

  // Handle modal close
  const handleClose = useCallback(() => {
    trackEvent('calendly_modal_closed', { cta_location: ctaLocation })
    setBookingSuccess(false)
    setBookingData(null)
    setIsLoading(true)
    setHasError(false)
    onClose()
  }, [onClose, ctaLocation])

  // Save booking data to Supabase
  const saveBookingToSupabase = useCallback(
    async (eventData: any) => {
      try {
        const payload = eventData.payload || eventData

        const bookingRecord = {
          calendly_id: payload.event?.uuid || `booking_${Date.now()}`,
          event_type: payload.event?.event_type?.name || 'Consultation',
          event_name: payload.event?.name || 'Booking Call',
          invitee_email: payload.invitee?.email || '<EMAIL>',
          invitee_name: payload.invitee?.name || 'Unknown Client',
          invitee_timezone: payload.invitee?.timezone || 'UTC',
          scheduled_at: payload.event?.start_time || new Date().toISOString(),
          start_time: payload.event?.start_time || new Date().toISOString(),
          end_time: payload.event?.end_time || new Date().toISOString(),
          status: 'active' as const,
          meeting_url: payload.event?.location?.join_url || '',
          location: payload.event?.location?.type || 'Online',
          source: 'website_popup',
          cta_location: ctaLocation,
          utm_source: 'portfolio',
          utm_medium: 'popup',
          utm_campaign: 'booking',
          raw_data: eventData,
        }

        const data = await db.bookings.create(bookingRecord)
        setBookingData(data)
        return true
      } catch (error) {
        console.error('Error saving booking:', error)
        return false
      }
    },
    [ctaLocation]
  )

  // Handle Calendly message events
  useEffect(() => {
    if (!isOpen) return

    const handleCalendlyMessage = async (e: MessageEvent) => {
      // Debug: Log all messages to see what we're receiving
      console.log('📨 Received message:', {
        origin: e.origin,
        data: e.data,
        event: e.data?.event,
        payload: e.data?.payload,
      })

      // Accept messages from Calendly
      if (e.origin !== 'https://calendly.com') return

      const { event, payload } = e.data

      // Track the event
      await trackEvent(`calendly_${event}`, {
        cta_location: ctaLocation,
        event_type: payload?.event_type?.name,
      })

      switch (event) {
        case 'calendly.profile_page_viewed':
        case 'calendly.event_type_viewed':
        case 'calendly.page_height':
          console.log('✅ Calendly loaded successfully, hiding loading state')
          setIsLoading(false)
          setHasError(false)
          break

        case 'calendly.date_and_time_selected':
          console.log('📅 Date and time selected')
          break

        case 'calendly.event_scheduled':
          console.log('🎉 Event scheduled!')
          setBookingSuccess(true)
          const saved = await saveBookingToSupabase(e.data)
          if (saved) {
            setTimeout(() => handleClose(), 3000)
          }
          break

        default:
          console.log('ℹ️ Other Calendly event:', event)
      }
    }

    window.addEventListener('message', handleCalendlyMessage)
    return () => window.removeEventListener('message', handleCalendlyMessage)
  }, [isOpen, ctaLocation, handleClose, saveBookingToSupabase])

  // Handle iframe events
  const handleIframeLoad = useCallback(() => {
    console.log('🔄 Calendly iframe loaded')
    // Give Calendly more time to initialize and send postMessage events
    setTimeout(() => {
      if (isLoading) {
        console.log('⏰ Timeout reached, hiding loading state as fallback')
        setIsLoading(false)
      }
    }, 5000) // Increased from 2s to 5s
  }, [isLoading])

  const handleIframeError = useCallback(() => {
    console.error('❌ Calendly iframe failed to load')
    setHasError(true)
    setIsLoading(false)
  }, [])

  // Handle escape key and modal open
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
      trackEvent('calendly_modal_opened', { cta_location: ctaLocation })
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'auto'
    }
  }, [isOpen, handleClose, ctaLocation])

  if (!isOpen) return null

  // Debug: Log the Calendly URL being used
  const iframeUrl = `${calendlyUrl}?embed_domain=${typeof window !== 'undefined' ? window.location.hostname : 'localhost'}&embed_type=Inline&hide_gdpr_banner=1`
  console.log('🔗 Calendly iframe URL:', iframeUrl)

  return (
    <div className="calendly-modal-overlay" onClick={handleClose}>
      <div
        className="calendly-modal-content"
        onClick={e => e.stopPropagation()}
      >
        <div className="calendly-modal-header">
          <h2>Schedule Your Free Strategy Session</h2>
          <button
            className="calendly-close-btn"
            onClick={handleClose}
            aria-label="Close"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path
                d="M18 6L6 18M6 6L18 18"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        <div className="calendly-modal-body">
          {bookingSuccess && (
            <div className="calendly-success">
              <div className="success-icon">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                  <circle
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="2"
                  />
                  <path
                    d="m9 12 2 2 4-4"
                    stroke="currentColor"
                    strokeWidth="2"
                  />
                </svg>
              </div>
              <h3>Booking Confirmed! 🎉</h3>
              <p>Your strategy session has been scheduled successfully.</p>
              <p>You'll receive a confirmation email from Calendly shortly.</p>
              {bookingData && (
                <div className="booking-details">
                  <p>
                    <strong>Event:</strong> {bookingData.event_name}
                  </p>
                  <p>
                    <strong>Date:</strong>{' '}
                    {new Date(bookingData.start_time).toLocaleDateString()}
                  </p>
                  <p>
                    <strong>Time:</strong>{' '}
                    {new Date(bookingData.start_time).toLocaleTimeString()}
                  </p>
                </div>
              )}
              <p className="success-note">
                This window will close automatically...
              </p>
            </div>
          )}

          {isLoading && !bookingSuccess && (
            <div className="calendly-loading">
              <div className="loading-spinner">
                <div className="spinner"></div>
              </div>
              <p>Loading your booking calendar...</p>
              <button
                onClick={() => {
                  console.log('🔧 Manual override: hiding loading state')
                  setIsLoading(false)
                }}
                style={{
                  marginTop: '1rem',
                  padding: '8px 16px',
                  background: 'rgba(23, 184, 221, 0.2)',
                  border: '1px solid rgba(23, 184, 221, 0.3)',
                  borderRadius: '6px',
                  color: '#17b8dd',
                  cursor: 'pointer',
                  fontSize: '0.875rem',
                }}
              >
                Skip Loading (Debug)
              </button>
            </div>
          )}

          {hasError && !bookingSuccess && (
            <div className="calendly-error">
              <div className="error-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                  <circle
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="2"
                  />
                  <line
                    x1="12"
                    y1="8"
                    x2="12"
                    y2="12"
                    stroke="currentColor"
                    strokeWidth="2"
                  />
                  <line
                    x1="12"
                    y1="16"
                    x2="12.01"
                    y2="16"
                    stroke="currentColor"
                    strokeWidth="2"
                  />
                </svg>
              </div>
              <h3>Unable to load calendar</h3>
              <p>
                We're having trouble loading the booking calendar. Please try
                the direct link below.
              </p>
              <a
                href={calendlyUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="calendly-fallback-btn"
              >
                Open Calendly in New Tab
              </a>
            </div>
          )}

          {!isLoading && !hasError && !bookingSuccess && (
            <div className="calendly-iframe-container">
              <iframe
                src={iframeUrl}
                width="100%"
                height="600"
                frameBorder="0"
                title="Schedule a meeting"
                onLoad={handleIframeLoad}
                onError={handleIframeError}
                style={{ border: 'none', borderRadius: '0 0 12px 12px' }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
