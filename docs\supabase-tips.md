The issue is clear from the logs: "Event tracking skipped: Supabase not configured" - your environment variables aren't being loaded properly in the client-side code. This is because you're trying to access process.env in client-side code, which doesn't work the same way in Next.js.
Fix the Environment Variable Loading
1. Update Your Calendly Modal Component
The issue is in how you're checking for environment variables on the client side:
'use client'

import { useEffect, useState, useCallback } from 'react'
import { createClient } from '@supabase/supabase-js'

interface CalendlyBookingModalProps {
  isOpen: boolean
  onClose: () => void
  calendlyUrl?: string
}

// Supabase client setup with validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Check if Supabase is configured
const isSupabaseConfigured = supabaseUrl && supabaseAnonKey

const supabase = isSupabaseConfigured 
  ? createClient(supabaseUrl, supabaseAnonKey)
  : null

export default function CalendlyBookingModal({
  isOpen,
  onClose,
  calendlyUrl = process.env.NEXT_PUBLIC_CALENDLY_URL || 'https://calendly.com/your-scheduling-link'
}: CalendlyBookingModalProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [bookingSuccess, setBookingSuccess] = useState(false)
  const [bookingData, setBookingData] = useState<any>(null)

  // Save booking data to Supabase (Free Plan - Message Events Only)
  const saveBookingToSupabase = async (eventData: any) => {
    // Check if Supabase is configured
    if (!isSupabaseConfigured) {
      console.warn('⚠️ Event tracking skipped: Supabase not configured')
      console.log('📋 Booking data would be saved:', eventData)
      return false
    }

    if (!supabase) {
      console.error('❌ Supabase client not initialized')
      return false
    }

    try {
      // Extract data from Calendly message event
      const payload = eventData.payload || eventData
      
      // Create booking record with available data
      const bookingRecord = {
        calendly_id: payload.event?.uuid || `booking_${Date.now()}`,
        event_type: payload.event?.event_type?.name || 'Consultation',
        event_name: payload.event?.name || 'Booking Call',
        invitee_email: payload.invitee?.email || '<EMAIL>',
        invitee_name: payload.invitee?.name || 'Unknown Client',
        invitee_timezone: payload.invitee?.timezone || 'UTC',
        scheduled_at: payload.event?.start_time || new Date().toISOString(),
        start_time: payload.event?.start_time || new Date().toISOString(),
        end_time: payload.event?.end_time || new Date().toISOString(),
        status: 'active',
        meeting_url: payload.event?.location?.join_url || '',
        location: payload.event?.location?.type || 'Online',
        source: 'website_popup',
        utm_source: 'portfolio',
        utm_medium: 'popup',
        utm_campaign: 'booking',
        raw_data: eventData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      console.log('💾 Attempting to save booking to Supabase:', bookingRecord)

      const { data, error } = await supabase
        .from('bookings')
        .insert([bookingRecord])
        .select()

      if (error) {
        console.error('❌ Error saving booking:', error)
        console.error('📋 Error details:', {
          message: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint
        })
        return false
      }

      console.log('✅ Booking saved successfully:', data)
      setBookingData(data[0])
      return true
    } catch (error) {
      console.error('❌ Error in saveBookingToSupabase:', error)
      return false
    }
  }

  // Handle Calendly message events (Free Plan Method)
  useEffect(() => {
    if (!isOpen) return

    const handleCalendlyMessage = async (e: MessageEvent) => {
      // Security: Only accept messages from Calendly
      if (e.origin !== 'https://calendly.com') return

      const { event, payload } = e.data

      console.log('📅 Calendly Event:', event, payload)

      switch (event) {
        case 'calendly.profile_page_viewed':
          console.log('👀 Profile page viewed')
          setIsLoading(false)
          setHasError(false)
          break

        case 'calendly.event_type_viewed':
          console.log('📋 Event type viewed')
          setIsLoading(false)
          setHasError(false)
          break

        case 'calendly.date_and_time_selected':
          console.log('🕐 Date and time selected')
          break

        case 'calendly.event_scheduled':
          console.log('🎉 Event scheduled!', payload)
          setBookingSuccess(true)
          
          // Save to Supabase
          const saved = await saveBookingToSupabase(e.data)
          if (saved) {
            console.log('✅ Booking data saved to database')
          }
          
          // Auto-close modal after 3 seconds
          setTimeout(() => {
            handleClose()
          }, 3000)
          break

        case 'calendly.page_height':
          // Handle iframe height changes
          console.log('📏 Page height changed:', payload)
          break

        default:
          console.log('ℹ️ Other Calendly event:', event, payload)
      }
    }

    window.addEventListener('message', handleCalendlyMessage)
    return () => window.removeEventListener('message', handleCalendlyMessage)
  }, [isOpen])

  // Handle iframe load events
  const handleIframeLoad = useCallback(() => {
    console.log('🔄 Calendly iframe loaded')
    // Give iframe time to initialize
    setTimeout(() => {
      if (isLoading) {
        setIsLoading(false)
      }
    }, 2000)
  }, [isLoading])

  // Handle iframe error
  const handleIframeError = useCallback(() => {
    console.error('❌ Calendly iframe failed to load')
    setHasError(true)
    setIsLoading(false)
  }, [])

  // Handle modal close
  const handleClose = useCallback(() => {
    setBookingSuccess(false)
    setBookingData(null)
    setIsLoading(true)
    setHasError(false)
    onClose()
  }, [onClose])

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'auto'
    }
  }, [isOpen, handleClose])

  // Don't render if not open
  if (!isOpen) return null

  return (
    <div className="calendly-modal-overlay" onClick={handleClose}>
      <div
        className="calendly-modal-content"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Modal Header */}
        <div className="calendly-modal-header">
          <h2>Schedule Your Call</h2>
          <button
            className="calendly-close-btn"
            onClick={handleClose}
            aria-label="Close booking modal"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path
                d="M18 6L6 18M6 6L18 18"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        {/* Modal Body */}
        <div className="calendly-modal-body">
          {/* Success State */}
          {bookingSuccess && (
            <div className="calendly-success">
              <div className="success-icon">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                  <path d="m9 12 2 2 4-4" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
              <h3>Booking Confirmed! 🎉</h3>
              <p>Your call has been scheduled successfully.</p>
              <p>You'll receive a confirmation email from Calendly shortly.</p>
              {bookingData && (
                <div className="booking-details">
                  <p><strong>Event:</strong> {bookingData.event_name}</p>
                  <p><strong>Date:</strong> {new Date(bookingData.start_time).toLocaleDateString()}</p>
                  <p><strong>Time:</strong> {new Date(bookingData.start_time).toLocaleTimeString()}</p>
                </div>
              )}
              <p className="success-note">This window will close automatically...</p>
            </div>
          )}

          {/* Loading State */}
          {isLoading && !bookingSuccess && (
            <div className="calendly-loading">
              <div className="loading-spinner">
                <div className="spinner"></div>
              </div>
              <p>Loading your booking calendar...</p>
            </div>
          )}

          {/* Error State */}
          {hasError && !bookingSuccess && (
            <div className="calendly-error">
              <div className="error-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                  <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" strokeWidth="2"/>
                  <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
              <h3>Unable to load calendar</h3>
              <p>We're having trouble loading the booking calendar. Please try the direct link below.</p>
              <a
                href={calendlyUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="calendly-fallback-btn"
              >
                Open Calendly in New Tab
              </a>
            </div>
          )}

          {/* Calendly Iframe */}
          {!isLoading && !hasError && !bookingSuccess && (
            <div className="calendly-iframe-container">
              <iframe
                src={`${calendlyUrl}?embed_domain=${typeof window !== 'undefined' ? window.location.hostname : 'localhost'}&embed_type=Inline&hide_gdpr_banner=1`}
                width="100%"
                height="600"
                frameBorder="0"
                title="Schedule a meeting"
                onLoad={handleIframeLoad}
                onError={handleIframeError}
                style={{ 
                  border: 'none',
                  borderRadius: '0 0 20px 20px'
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

Create a Proper Environment Configuration
Create a new file to centralize your environment configuration:
// lib/env.ts
// Environment configuration with validation and debugging

export const ENV_CONFIG = {
  // Supabase Configuration
  SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
  SUPABASE_SERVICE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  
  // Calendly Configuration
  CALENDLY_URL: process.env.NEXT_PUBLIC_CALENDLY_URL || '',
  
  // Environment Info
  NODE_ENV: process.env.NODE_ENV || 'development',
  VERCEL_ENV: process.env.VERCEL_ENV || 'development',
  
  // Validation flags
  get isSupabaseConfigured() {
    return Boolean(this.SUPABASE_URL && this.SUPABASE_ANON_KEY)
  },
  
  get isCalendlyConfigured() {
    return Boolean(this.CALENDLY_URL)
  },
  
  get isProduction() {
    return this.NODE_ENV === 'production' || this.VERCEL_ENV === 'production'
  }
}

// Debug function to log environment status
export function debugEnvironment() {
  const debug = {
    timestamp: new Date().toISOString(),
    environment: ENV_CONFIG.NODE_ENV,
    vercelEnv: ENV_CONFIG.VERCEL_ENV,
    supabase: {
      url: ENV_CONFIG.SUPABASE_URL ? `✅ Set (${ENV_CONFIG.SUPABASE_URL.substring(0, 30)}...)` : '❌ Not set',
      anonKey: ENV_CONFIG.SUPABASE_ANON_KEY ? `✅ Set (${ENV_CONFIG.SUPABASE_ANON_KEY.length} chars)` : '❌ Not set',
      serviceKey: ENV_CONFIG.SUPABASE_SERVICE_KEY ? `✅ Set (${ENV_CONFIG.SUPABASE_SERVICE_KEY.length} chars)` : '❌ Not set',
      configured: ENV_CONFIG.isSupabaseConfigured ? '✅ Yes' : '❌ No'
    },
    calendly: {
      url: ENV_CONFIG.CALENDLY_URL ? `✅ Set (${ENV_CONFIG.CALENDLY_URL})` : '❌ Not set',
      configured: ENV_CONFIG.isCalendlyConfigured ? '✅ Yes' : '❌ No'
    },
    clientSide: typeof window !== 'undefined' ? 'Yes' : 'No'
  }
  
  console.log('🔍 Environment Debug:', debug)
  return debug
}

// Validation function
export function validateEnvironment() {
  const errors = []
  
  if (!ENV_CONFIG.SUPABASE_URL) {
    errors.push('NEXT_PUBLIC_SUPABASE_URL is not set')
  }
  
  if (!ENV_CONFIG.SUPABASE_ANON_KEY) {
    errors.push('NEXT_PUBLIC_SUPABASE_ANON_KEY is not set')
  }
  
  if (!ENV_CONFIG.CALENDLY_URL) {
    errors.push('NEXT_PUBLIC_CALENDLY_URL is not set')
  }
  
  if (errors.length > 0) {
    console.error('❌ Environment validation failed:', errors)
    return { valid: false, errors }
  }
  
  console.log('✅ Environment validation passed')
  return { valid: true, errors: [] }
}

// Export individual values for convenience
export const {
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
  SUPABASE_SERVICE_KEY,
  CALENDLY_URL,
  NODE_ENV,
  VERCEL_ENV
} = ENV_CONFIG

Update Your Calendly Modal to Use the New Config
'use client'

import { useEffect, useState, useCallback } from 'react'
import { createClient } from '@supabase/supabase-js'

interface CalendlyBookingModalProps {
  isOpen: boolean
  onClose: () => void
  calendlyUrl?: string
}

// Supabase client setup with validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Check if Supabase is configured
const isSupabaseConfigured = supabaseUrl && supabaseAnonKey

const supabase = isSupabaseConfigured 
  ? createClient(supabaseUrl, supabaseAnonKey)
  : null

export default function CalendlyBookingModal({
  isOpen,
  onClose,
  calendlyUrl = process.env.NEXT_PUBLIC_CALENDLY_URL || 'https://calendly.com/your-scheduling-link'
}: CalendlyBookingModalProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [bookingSuccess, setBookingSuccess] = useState(false)
  const [bookingData, setBookingData] = useState<any>(null)

  // Save booking data to Supabase (Free Plan - Message Events Only)
  const saveBookingToSupabase = async (eventData: any) => {
    // Check if Supabase is configured
    if (!ENV_CONFIG.isSupabaseConfigured) {
      console.warn('⚠️ Database not configured, booking not saved:', 'Booking received but not saved to database')
      console.log('📋 Booking data (would be saved if configured):', eventData)
      return false
    }

    if (!supabase) {
      console.error('❌ Supabase client not initialized')
      return false
    }

    try {
      // Extract data from Calendly message event
      const payload = eventData.payload || eventData
      
      // Create booking record with available data
      const bookingRecord = {
        calendly_id: payload.event?.uuid || `booking_${Date.now()}`,
        event_type: payload.event?.event_type?.name || 'Consultation',
        event_name: payload.event?.name || 'Booking Call',
        invitee_email: payload.invitee?.email || '<EMAIL>',
        invitee_name: payload.invitee?.name || 'Unknown Client',
        invitee_timezone: payload.invitee?.timezone || 'UTC',
        scheduled_at: payload.event?.start_time || new Date().toISOString(),
        start_time: payload.event?.start_time || new Date().toISOString(),
        end_time: payload.event?.end_time || new Date().toISOString(),
        status: 'active',
        meeting_url: payload.event?.location?.join_url || '',
        location: payload.event?.location?.type || 'Online',
        source: 'website_popup',
        utm_source: 'portfolio',
        utm_medium: 'popup',
        utm_campaign: 'booking',
        raw_data: eventData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      console.log('💾 Attempting to save booking to Supabase:', bookingRecord)

      const { data, error } = await supabase
        .from('bookings')
        .insert([bookingRecord])
        .select()

      if (error) {
        console.error('❌ Error saving booking to Supabase:', error)
        console.error('📋 Error details:', {
          message: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint
        })
        return false
      }

      console.log('✅ Booking saved successfully to Supabase:', data)
      setBookingData(data[0])
      return true
    } catch (error) {
      console.error('❌ Error in saveBookingToSupabase:', error)
      return false
    }
  }

  // Handle Calendly message events (Free Plan Method)
  useEffect(() => {
    if (!isOpen) return

    const handleCalendlyMessage = async (e: MessageEvent) => {
      // Security: Only accept messages from Calendly
      if (e.origin !== 'https://calendly.com') return

      const { event, payload } = e.data

      console.log('📅 Calendly Event:', event, payload)

      switch (event) {
        case 'calendly.profile_page_viewed':
          console.log('👀 Profile page viewed')
          setIsLoading(false)
          setHasError(false)
          break

        case 'calendly.event_type_viewed':
          console.log('📋 Event type viewed')
          setIsLoading(false)
          setHasError(false)
          break

        case 'calendly.date_and_time_selected':
          console.log('🕐 Date and time selected')
          break

        case 'calendly.event_scheduled':
          console.log('🎉 Event scheduled!', payload)
          setBookingSuccess(true)
          
          // Save to Supabase
          const saved = await saveBookingToSupabase(e.data)
          if (saved) {
            console.log('✅ Booking data saved to database')
          }
          
          // Auto-close modal after 3 seconds
          setTimeout(() => {
            handleClose()
          }, 3000)
          break

        case 'calendly.page_height':
          // Handle iframe height changes
          console.log('📏 Page height changed:', payload)
          break

        default:
          console.log('ℹ️ Other Calendly event:', event, payload)
      }
    }

    window.addEventListener('message', handleCalendlyMessage)
    return () => window.removeEventListener('message', handleCalendlyMessage)
  }, [isOpen])

  // Handle iframe load events
  const handleIframeLoad = useCallback(() => {
    console.log('🔄 Calendly iframe loaded')
    // Give iframe time to initialize
    setTimeout(() => {
      if (isLoading) {
        setIsLoading(false)
      }
    }, 2000)
  }, [isLoading])

  // Handle iframe error
  const handleIframeError = useCallback(() => {
    console.error('❌ Calendly iframe failed to load')
    setHasError(true)
    setIsLoading(false)
  }, [])

  // Handle modal close
  const handleClose = useCallback(() => {
    setBookingSuccess(false)
    setBookingData(null)
    setIsLoading(true)
    setHasError(false)
    onClose()
  }, [onClose])

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'auto'
    }
  }, [isOpen, handleClose])

  // Don't render if not open
  if (!isOpen) return null

  return (
    <div className="calendly-modal-overlay" onClick={handleClose}>
      <div
        className="calendly-modal-content"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Modal Header */}
        <div className="calendly-modal-header">
          <h2>Schedule Your Call</h2>
          <button
            className="calendly-close-btn"
            onClick={handleClose}
            aria-label="Close booking modal"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path
                d="M18 6L6 18M6 6L18 18"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        {/* Modal Body */}
        <div className="calendly-modal-body">
          {/* Success State */}
          {bookingSuccess && (
            <div className="calendly-success">
              <div className="success-icon">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                  <path d="m9 12 2 2 4-4" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
              <h3>Booking Confirmed! 🎉</h3>
              <p>Your call has been scheduled successfully.</p>
              <p>You'll receive a confirmation email from Calendly shortly.</p>
              {bookingData && (
                <div className="booking-details">
                  <p><strong>Event:</strong> {bookingData.event_name}</p>
                  <p><strong>Date:</strong> {new Date(bookingData.start_time).toLocaleDateString()}</p>
                  <p><strong>Time:</strong> {new Date(bookingData.start_time).toLocaleTimeString()}</p>
                </div>
              )}
              <p className="success-note">This window will close automatically...</p>
            </div>
          )}

          {/* Loading State */}
          {isLoading && !bookingSuccess && (
            <div className="calendly-loading">
              <div className="loading-spinner">
                <div className="spinner"></div>
              </div>
              <p>Loading your booking calendar...</p>
            </div>
          )}

          {/* Error State */}
          {hasError && !bookingSuccess && (
            <div className="calendly-error">
              <div className="error-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                  <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" strokeWidth="2"/>
                  <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
              <h3>Unable to load calendar</h3>
              <p>We're having trouble loading the booking calendar. Please try the direct link below.</p>
              <a
                href={calendlyUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="calendly-fallback-btn"
              >
                Open Calendly in New Tab
              </a>
            </div>
          )}

          {/* Calendly Iframe */}
          {!isLoading && !hasError && !bookingSuccess && (
            <div className="calendly-iframe-container">
              <iframe
                src={`${calendlyUrl}?embed_domain=${typeof window !== 'undefined' ? window.location.hostname : 'localhost'}&embed_type=Inline&hide_gdpr_banner=1`}
                width="100%"
                height="600"
                frameBorder="0"
                title="Schedule a meeting"
                onLoad={handleIframeLoad}
                onError={handleIframeError}
                style={{ 
                  border: 'none',
                  borderRadius: '0 0 20px 20px'
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
Test Environment Variables
Create a simple test page to verify your environment variables are loaded:
// app/test-env/page.tsx
'use client'

import { useEffect, useState } from 'react'
import { ENV_CONFIG, debugEnvironment } from '@/lib/env'

export default function TestEnvironment() {
  const [envData, setEnvData] = useState<any>(null)

  useEffect(() => {
    const debug = debugEnvironment()
    setEnvData(debug)
  }, [])

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h1>Environment Variables Test</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>Environment Status:</h2>
        <p>Supabase Configured: {ENV_CONFIG.isSupabaseConfigured ? '✅ Yes' : '❌ No'}</p>
        <p>Calendly Configured: {ENV_CONFIG.isCalendlyConfigured ? '✅ Yes' : '❌ No'}</p>
        <p>Environment: {ENV_CONFIG.NODE_ENV}</p>
        <p>Vercel Environment: {ENV_CONFIG.VERCEL_ENV}</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Raw Environment Variables:</h2>
        <p>NEXT_PUBLIC_SUPABASE_URL: {process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set'}</p>
        <p>NEXT_PUBLIC_SUPABASE_ANON_KEY: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set'}</p>
        <p>NEXT_PUBLIC_CALENDLY_URL: {process.env.NEXT_PUBLIC_CALENDLY_URL ? 'Set' : 'Not set'}</p>
      </div>

      <div>
        <h2>Debug Data:</h2>
        <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
          {JSON.stringify(envData, null, 2)}
        </pre>
      </div>
    </div>
  )
}