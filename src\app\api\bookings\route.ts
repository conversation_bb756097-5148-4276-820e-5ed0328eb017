import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Environment variable validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// Check if environment variables are available
const hasValidConfig =
  supabaseUrl &&
  supabaseServiceKey &&
  supabaseUrl !== 'https://placeholder.supabase.co' &&
  supabaseServiceKey !== 'placeholder-key'

// Only create client if we have valid config
let supabaseAdmin: any = null
if (hasValidConfig) {
  supabaseAdmin = createClient(supabaseUrl!, supabaseServiceKey!, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  })
}

export async function POST(request: NextRequest) {
  try {
    // Check if Supabase is configured
    if (!hasValidConfig || !supabaseAdmin) {
      console.log('⚠️ Supabase not configured, skipping database save')
      return NextResponse.json(
        {
          success: false,
          error: 'Database not configured',
          message: 'Booking received but not saved to database',
        },
        { status: 200 } // Return 200 so the frontend doesn't show an error
      )
    }

    const body = await request.json()

    // Validate required fields
    if (!body.calendly_id || !body.invitee_email) {
      return NextResponse.json(
        { error: 'Missing required fields: calendly_id, invitee_email' },
        { status: 400 }
      )
    }

    // Prepare booking record
    const bookingRecord = {
      calendly_id: body.calendly_id,
      event_type: body.event_type || 'Consultation',
      event_name: body.event_name || 'Booking Call',
      invitee_email: body.invitee_email,
      invitee_name: body.invitee_name || 'Unknown Client',
      invitee_timezone: body.invitee_timezone || 'UTC',
      scheduled_at: body.scheduled_at || new Date().toISOString(),
      start_time: body.start_time || new Date().toISOString(),
      end_time: body.end_time || new Date().toISOString(),
      status: body.status || 'active',
      meeting_url: body.meeting_url || '',
      location: body.location || 'Online',
      source: body.source || 'website_popup',
      cta_location: body.cta_location || 'unknown',
      utm_source: body.utm_source || 'portfolio',
      utm_medium: body.utm_medium || 'popup',
      utm_campaign: body.utm_campaign || 'booking',
      raw_data: body.raw_data || body,
      updated_at: new Date().toISOString(),
    }

    // Insert into Supabase using service role
    const { data, error } = await supabaseAdmin
      .from('bookings')
      .insert([bookingRecord])
      .select()
      .single()

    if (error) {
      console.error('Supabase error:', error)
      return NextResponse.json(
        { error: 'Failed to save booking', details: error.message },
        { status: 500 }
      )
    }

    console.log('✅ Booking saved successfully:', data.id)
    return NextResponse.json({ success: true, data })
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Bookings API endpoint',
    configured: hasValidConfig,
    supabaseUrl: supabaseUrl ? 'Set' : 'Not set',
    serviceKey: supabaseServiceKey ? 'Set' : 'Not set',
  })
}
