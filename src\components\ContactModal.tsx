'use client'

import { useEffect, useState } from 'react'

export default function ContactModal() {
  const [showContactForm, setShowContactForm] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: '',
    projectType: 'automation',
  })
  useEffect(() => {
    // Modal functionality
    const modal = document.getElementById('contactModal')
    const closeBtn = document.querySelector('.close-button')

    const closeModal = () => {
      if (modal) {
        modal.classList.remove('active')
        document.body.style.overflow = 'auto'
        setShowContactForm(false)
      }
    }

    // Close modal when clicking close button
    if (closeBtn) {
      closeBtn.addEventListener('click', closeModal)
    }

    // Close modal when clicking outside
    if (modal) {
      modal.addEventListener('click', e => {
        if (e.target === modal) {
          closeModal()
        }
      })
    }

    // Close modal with Escape key
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        closeModal()
      }
    }
    document.addEventListener('keydown', handleEscape)

    return () => {
      if (closeBtn) {
        closeBtn.removeEventListener('click', closeModal)
      }
      if (modal) {
        modal.removeEventListener('click', closeModal)
      }
      document.removeEventListener('keydown', handleEscape)
    }
  }, [])

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Create mailto link with form data
    const subject = `AI Automation Inquiry - ${formData.projectType}`
    const body = `Hi Denis,

I'm interested in your AI automation services. Here are my details:

Name: ${formData.name}
Email: ${formData.email}
Company: ${formData.company}
Project Type: ${formData.projectType}

Message:
${formData.message}

Looking forward to hearing from you!`

    const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
    window.location.href = mailtoLink
  }

  return (
    <div id="contactModal" className="modal">
      <div className="modal-content">
        <span className="close-button">&times;</span>
        <h2 className="modal-headline">We&apos;d Love to Hear From You!</h2>
        <p className="modal-subheadline">
          Choose how you&apos;d like to connect:
        </p>

        <div className="contact-options">
          <a
            href="mailto:<EMAIL>?subject=AI%20Automation%20Inquiry&body=Hi%20Denis,%0A%0AI'm%20interested%20in%20learning%20more%20about%20your%20AI%20automation%20services.%0A%0ABusiness:%20%0AChallenge:%20%0A%0ALooking%20forward%20to%20hearing%20from%20you!"
            className="contact-card"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="48"
              height="48"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="icon lucide lucide-mail"
            >
              <rect width="20" height="16" x="2" y="4" rx="2" />
              <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
            </svg>
            <h4>Send an Email</h4>
            <p>Get a detailed response within 24 hours</p>
          </a>

          <a
            href="https://calendly.com/your-scheduling-link"
            target="_blank"
            className="contact-card"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="48"
              height="48"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="icon lucide lucide-calendar-check"
            >
              <rect width="18" height="18" x="3" y="4" rx="2" ry="2" />
              <line x1="16" x2="16" y1="2" y2="6" />
              <line x1="8" x2="8" y1="2" y2="6" />
              <line x1="3" x2="21" y1="10" y2="10" />
              <path d="m9 16 2 2 4-4" />
            </svg>
            <h4>Book a 30-min Call</h4>
            <p>Free strategy session to discuss your needs</p>
          </a>

          <button
            className="contact-card"
            id="openContactForm"
            onClick={() => setShowContactForm(true)}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="48"
              height="48"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="icon lucide lucide-form-input"
            >
              <rect width="20" height="12" x="2" y="6" rx="2" />
              <path d="M12 12h.01" />
              <path d="M17 12h.01" />
              <path d="M7 12h.01" />
            </svg>
            <h4>Contact Form</h4>
            <p>Detailed project inquiry form</p>
          </button>
        </div>

        {showContactForm && (
          <div className="contact-form-overlay">
            <div className="contact-form-container">
              <button
                className="form-back-btn"
                onClick={() => setShowContactForm(false)}
              >
                ← Back to Options
              </button>

              <h3>Contact Form</h3>
              <p>
                Tell us about your project and we'll get back to you within 24
                hours.
              </p>

              <form onSubmit={handleSubmit} className="contact-form">
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="name">Name *</label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="email">Email *</label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="company">Company</label>
                    <input
                      type="text"
                      id="company"
                      name="company"
                      value={formData.company}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="projectType">Project Type</label>
                    <select
                      id="projectType"
                      name="projectType"
                      value={formData.projectType}
                      onChange={handleInputChange}
                    >
                      <option value="automation">AI Automation</option>
                      <option value="chatbot">AI Chatbot</option>
                      <option value="optimization">Process Optimization</option>
                      <option value="integration">System Integration</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="message">Project Details *</label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows={4}
                    placeholder="Tell us about your current challenges and what you'd like to achieve..."
                    required
                  />
                </div>

                <button type="submit" className="btn btn-primary form-submit">
                  Send Message
                </button>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
